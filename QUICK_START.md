# 🚀 Guia <PERSON> Início <PERSON>pido - CVAT LED Bot

## Instalação Automática

```bash
# 1. Execute o script de setup
python setup.py

# 2. Ative o ambiente virtual
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

## Configuração Rápida

1. **Edite o arquivo `.env`**:
```env
CVAT_HOST=http://localhost:8080
CVAT_USERNAME=seu_usuario
CVAT_PASSWORD=sua_senha
```

2. **Prepare o CVAT**:
   - Crie um projeto com os labels: `LED_FUNCIONAL` e `LED_COM_DEFEITO`
   - Crie uma tarefa com imagens de placas eletrônicas
   - Anote o ID da tarefa

## Teste Rápido

```bash
# Testar detecção em uma imagem local
python test_detection.py caminho/para/sua/imagem.jpg

# Executar o bot em uma tarefa do CVAT
python main.py <TASK_ID>
```

## Calibração de Cores (Opcional)

Se os LEDs não estão sendo detectados corretamente:

```bash
# Abrir ferramenta de calibração
python calibrate_colors.py caminho/para/imagem_com_leds.jpg

# Seguir instruções na tela:
# 1. Selecionar regiões de LEDs funcionais e defeituosos
# 2. Ajustar faixas HSV com os controles
# 3. Salvar configuração (tecla 's')
```

## Comandos Úteis

```bash
# Modo debug com imagens salvas
python main.py <TASK_ID> --debug --save-debug-images

# Usar configuração personalizada
python main.py <TASK_ID> --config config_personalizado.json

# Modo dry-run (não faz alterações)
python main.py <TASK_ID> --dry-run
```

## Estrutura do Projeto

```
cvat-led-bot/
├── main.py              # 🎯 Script principal
├── test_detection.py    # 🧪 Teste local de detecção
├── calibrate_colors.py  # 🎨 Calibração de cores
├── setup.py            # ⚙️ Instalação automática
├── config.py           # 📋 Configurações
├── cvat_bot.py         # 🤖 Integração CVAT
├── led_detector.py     # 🔍 Detecção de LEDs
├── color_analyzer.py   # 🌈 Análise de cores
└── README.md           # 📚 Documentação completa
```

## Solução de Problemas

### ❌ Erro de conexão com CVAT
- Verifique se o CVAT está rodando
- Confirme host, usuário e senha no `.env`

### ❌ LEDs não detectados
- Use `test_detection.py` para debug local
- Ajuste parâmetros em `config.py`
- Use `calibrate_colors.py` para cores

### ❌ Classificação incorreta
- Calibre as faixas de cor HSV
- Ajuste `color_confidence_threshold`
- Verifique iluminação das imagens

## Próximos Passos

1. 📖 Leia o `README.md` completo
2. 🔧 Ajuste configurações em `config.py`
3. 🎯 Execute em suas tarefas do CVAT
4. 📊 Analise os resultados e ajuste conforme necessário

---

💡 **Dica**: Comece sempre testando com `test_detection.py` antes de usar no CVAT!
