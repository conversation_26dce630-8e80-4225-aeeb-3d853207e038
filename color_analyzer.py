"""
Analisador de cores para classificar LEDs como funcionais ou com defeito
"""
import cv2
import numpy as np
from typing import Tuple, Optional, List
import logging
from enum import Enum
from config import ColorAnalysisConfig

logger = logging.getLogger(__name__)

class LEDStatus(Enum):
    """Status do LED baseado na análise de cor"""
    FUNCTIONAL = "functional"  # LED verde (funcionando)
    DEFECTIVE = "defective"    # LED vermelho (com defeito)
    UNKNOWN = "unknown"        # Cor não identificada

class ColorAnalyzer:
    """Classe para analisar cores de LEDs e classificar seu status"""
    
    def __init__(self, config: ColorAnalysisConfig):
        self.config = config
        
    def analyze_led_color(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> Tuple[LEDStatus, float]:
        """
        Analisa a cor de um LED e determina seu status
        
        Args:
            image: Imagem BGR original
            bbox: Bounding box do LED (x, y, w, h)
            
        Returns:
            Tupla com (status_do_led, confiança)
        """
        x, y, w, h = bbox
        
        # Expandir a região de análise
        padding = self.config.analysis_region_padding
        x_start = max(0, x - padding)
        y_start = max(0, y - padding)
        x_end = min(image.shape[1], x + w + padding)
        y_end = min(image.shape[0], y + h + padding)
        
        # Extrair região do LED
        led_region = image[y_start:y_end, x_start:x_end]
        
        if led_region.size == 0:
            return LEDStatus.UNKNOWN, 0.0
        
        # Converter para HSV para melhor análise de cor
        hsv_region = cv2.cvtColor(led_region, cv2.COLOR_BGR2HSV)
        
        # Analisar cores funcionais (verde)
        functional_confidence = self._analyze_color_ranges(
            hsv_region, self.config.functional_led_hsv_ranges
        )
        
        # Analisar cores defeituosas (vermelho)
        defective_confidence = self._analyze_color_ranges(
            hsv_region, self.config.defective_led_hsv_ranges
        )
        
        # Determinar status baseado na maior confiança
        if functional_confidence > defective_confidence and functional_confidence > self.config.color_confidence_threshold:
            return LEDStatus.FUNCTIONAL, functional_confidence
        elif defective_confidence > self.config.color_confidence_threshold:
            return LEDStatus.DEFECTIVE, defective_confidence
        else:
            return LEDStatus.UNKNOWN, max(functional_confidence, defective_confidence)
    
    def _analyze_color_ranges(self, hsv_region: np.ndarray, color_ranges: List[Tuple[Tuple[int, int, int], Tuple[int, int, int]]]) -> float:
        """
        Analisa uma região HSV contra faixas de cores específicas
        
        Args:
            hsv_region: Região da imagem em HSV
            color_ranges: Lista de faixas de cor (min_hsv, max_hsv)
            
        Returns:
            Confiança da detecção da cor (0.0 a 1.0)
        """
        total_pixels = hsv_region.shape[0] * hsv_region.shape[1]
        if total_pixels == 0:
            return 0.0
        
        matching_pixels = 0
        
        for (lower_hsv, upper_hsv) in color_ranges:
            # Criar máscara para a faixa de cor
            mask = cv2.inRange(hsv_region, np.array(lower_hsv), np.array(upper_hsv))
            matching_pixels += np.sum(mask > 0)
        
        # Calcular porcentagem de pixels que correspondem
        confidence = min(1.0, matching_pixels / total_pixels)
        
        return confidence
    
    def get_dominant_color(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> Tuple[int, int, int]:
        """
        Obtém a cor dominante na região do LED
        
        Args:
            image: Imagem BGR original
            bbox: Bounding box do LED (x, y, w, h)
            
        Returns:
            Cor dominante em BGR
        """
        x, y, w, h = bbox
        led_region = image[y:y+h, x:x+w]
        
        if led_region.size == 0:
            return (0, 0, 0)
        
        # Redimensionar para acelerar o processamento
        small_region = cv2.resize(led_region, (50, 50))
        
        # Converter para formato adequado para k-means
        data = small_region.reshape((-1, 3))
        data = np.float32(data)
        
        # Aplicar k-means para encontrar cores dominantes
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(data, 3, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # Encontrar a cor mais frequente
        unique_labels, counts = np.unique(labels, return_counts=True)
        dominant_color_idx = unique_labels[np.argmax(counts)]
        dominant_color = centers[dominant_color_idx]
        
        return tuple(map(int, dominant_color))
    
    def visualize_color_analysis(self, image: np.ndarray, bbox: Tuple[int, int, int, int], 
                                status: LEDStatus, confidence: float) -> np.ndarray:
        """
        Visualiza o resultado da análise de cor
        
        Args:
            image: Imagem original
            bbox: Bounding box do LED
            status: Status determinado
            confidence: Confiança da classificação
            
        Returns:
            Imagem com a visualização
        """
        result = image.copy()
        x, y, w, h = bbox
        
        # Definir cor da anotação baseada no status
        if status == LEDStatus.FUNCTIONAL:
            color = (0, 255, 0)  # Verde
            label = "FUNCIONAL"
        elif status == LEDStatus.DEFECTIVE:
            color = (0, 0, 255)  # Vermelho
            label = "DEFEITO"
        else:
            color = (128, 128, 128)  # Cinza
            label = "DESCONHECIDO"
        
        # Desenhar bounding box
        cv2.rectangle(result, (x, y), (x + w, y + h), color, 2)
        
        # Adicionar texto com status e confiança
        text = f"{label} ({confidence:.2f})"
        text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
        
        # Fundo para o texto
        cv2.rectangle(result, (x, y - text_size[1] - 10), 
                     (x + text_size[0], y), color, -1)
        
        # Texto
        cv2.putText(result, text, (x, y - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result
    
    def create_color_mask(self, image: np.ndarray, color_ranges: List[Tuple[Tuple[int, int, int], Tuple[int, int, int]]]) -> np.ndarray:
        """
        Cria uma máscara para destacar regiões com cores específicas
        
        Args:
            image: Imagem BGR
            color_ranges: Faixas de cor em HSV
            
        Returns:
            Máscara binária
        """
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        
        for (lower_hsv, upper_hsv) in color_ranges:
            range_mask = cv2.inRange(hsv, np.array(lower_hsv), np.array(upper_hsv))
            mask = cv2.bitwise_or(mask, range_mask)
        
        return mask
    
    def get_color_statistics(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> dict:
        """
        Obtém estatísticas detalhadas sobre as cores na região do LED
        
        Args:
            image: Imagem BGR original
            bbox: Bounding box do LED
            
        Returns:
            Dicionário com estatísticas de cor
        """
        x, y, w, h = bbox
        led_region = image[y:y+h, x:x+w]
        
        if led_region.size == 0:
            return {}
        
        # Converter para diferentes espaços de cor
        hsv_region = cv2.cvtColor(led_region, cv2.COLOR_BGR2HSV)
        lab_region = cv2.cvtColor(led_region, cv2.COLOR_BGR2LAB)
        
        # Calcular estatísticas
        stats = {
            'bgr_mean': np.mean(led_region, axis=(0, 1)).tolist(),
            'bgr_std': np.std(led_region, axis=(0, 1)).tolist(),
            'hsv_mean': np.mean(hsv_region, axis=(0, 1)).tolist(),
            'hsv_std': np.std(hsv_region, axis=(0, 1)).tolist(),
            'brightness': np.mean(cv2.cvtColor(led_region, cv2.COLOR_BGR2GRAY)),
            'dominant_color': self.get_dominant_color(image, bbox)
        }
        
        return stats
