#!/usr/bin/env python3
"""
Script para calibrar as faixas de cores dos LEDs
"""
import argparse
import cv2
import numpy as np
from pathlib import Path
import json
import logging

def setup_logging():
    """Configura logging básico"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

class ColorCalibrator:
    """Classe para calibrar faixas de cores interativamente"""
    
    def __init__(self, image_path: str):
        self.image = cv2.imread(image_path)
        if self.image is None:
            raise ValueError(f"Não foi possível carregar a imagem: {image_path}")
        
        self.hsv_image = cv2.cvtColor(self.image, cv2.COLOR_BGR2HSV)
        self.selected_regions = []
        self.current_selection = None
        self.selecting = False
        
        # Configurar callback do mouse
        cv2.namedWindow('Calibração de Cores')
        cv2.setMouseCallback('Calibração de Cores', self.mouse_callback)
        
        # Criar trackbars para ajuste de HSV
        cv2.namedWindow('Controles HSV')
        cv2.createTrackbar('H Min', 'Controles HSV', 0, 179, lambda x: None)
        cv2.createTrackbar('S Min', 'Controles HSV', 0, 255, lambda x: None)
        cv2.createTrackbar('V Min', 'Controles HSV', 0, 255, lambda x: None)
        cv2.createTrackbar('H Max', 'Controles HSV', 179, 179, lambda x: None)
        cv2.createTrackbar('S Max', 'Controles HSV', 255, 255, lambda x: None)
        cv2.createTrackbar('V Max', 'Controles HSV', 255, 255, lambda x: None)
    
    def mouse_callback(self, event, x, y, flags, param):
        """Callback para seleção de regiões com o mouse"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.selecting = True
            self.current_selection = [(x, y), (x, y)]
        
        elif event == cv2.EVENT_MOUSEMOVE and self.selecting:
            self.current_selection[1] = (x, y)
        
        elif event == cv2.EVENT_LBUTTONUP:
            self.selecting = False
            if self.current_selection:
                self.selected_regions.append(self.current_selection.copy())
                self.analyze_selected_region(self.current_selection)
                self.current_selection = None
    
    def analyze_selected_region(self, region):
        """Analisa a região selecionada e sugere faixas HSV"""
        (x1, y1), (x2, y2) = region
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        # Extrair região HSV
        roi_hsv = self.hsv_image[y1:y2, x1:x2]
        
        if roi_hsv.size == 0:
            return
        
        # Calcular estatísticas
        h_values = roi_hsv[:, :, 0].flatten()
        s_values = roi_hsv[:, :, 1].flatten()
        v_values = roi_hsv[:, :, 2].flatten()
        
        # Calcular faixas (média ± 2 desvios padrão)
        h_mean, h_std = np.mean(h_values), np.std(h_values)
        s_mean, s_std = np.mean(s_values), np.std(s_values)
        v_mean, v_std = np.mean(v_values), np.std(v_values)
        
        h_min = max(0, int(h_mean - 2 * h_std))
        h_max = min(179, int(h_mean + 2 * h_std))
        s_min = max(0, int(s_mean - 2 * s_std))
        s_max = min(255, int(s_mean + 2 * s_std))
        v_min = max(0, int(v_mean - 2 * v_std))
        v_max = min(255, int(v_mean + 2 * v_std))
        
        print(f"\nRegião selecionada: ({x1}, {y1}) - ({x2}, {y2})")
        print(f"Faixa HSV sugerida:")
        print(f"  H: {h_min} - {h_max} (média: {h_mean:.1f})")
        print(f"  S: {s_min} - {s_max} (média: {s_mean:.1f})")
        print(f"  V: {v_min} - {v_max} (média: {v_mean:.1f})")
        print(f"Formato para config: [[{h_min}, {s_min}, {v_min}], [{h_max}, {s_max}, {v_max}]]")
        
        # Atualizar trackbars
        cv2.setTrackbarPos('H Min', 'Controles HSV', h_min)
        cv2.setTrackbarPos('S Min', 'Controles HSV', s_min)
        cv2.setTrackbarPos('V Min', 'Controles HSV', v_min)
        cv2.setTrackbarPos('H Max', 'Controles HSV', h_max)
        cv2.setTrackbarPos('S Max', 'Controles HSV', s_max)
        cv2.setTrackbarPos('V Max', 'Controles HSV', v_max)
    
    def get_current_hsv_range(self):
        """Obtém a faixa HSV atual dos trackbars"""
        h_min = cv2.getTrackbarPos('H Min', 'Controles HSV')
        s_min = cv2.getTrackbarPos('S Min', 'Controles HSV')
        v_min = cv2.getTrackbarPos('V Min', 'Controles HSV')
        h_max = cv2.getTrackbarPos('H Max', 'Controles HSV')
        s_max = cv2.getTrackbarPos('S Max', 'Controles HSV')
        v_max = cv2.getTrackbarPos('V Max', 'Controles HSV')
        
        return (h_min, s_min, v_min), (h_max, s_max, v_max)
    
    def create_mask(self, lower_hsv, upper_hsv):
        """Cria máscara para a faixa HSV especificada"""
        return cv2.inRange(self.hsv_image, np.array(lower_hsv), np.array(upper_hsv))
    
    def run(self):
        """Executa o calibrador interativo"""
        print("=== Calibrador de Cores para LEDs ===")
        print("Instruções:")
        print("1. Clique e arraste para selecionar regiões de LEDs")
        print("2. Use os trackbars para ajustar a faixa HSV")
        print("3. Pressione 's' para salvar a configuração atual")
        print("4. Pressione 'r' para resetar seleções")
        print("5. Pressione 'q' para sair")
        print()
        
        saved_ranges = []
        
        while True:
            # Criar imagem de visualização
            display_image = self.image.copy()
            
            # Desenhar regiões selecionadas
            for i, region in enumerate(self.selected_regions):
                (x1, y1), (x2, y2) = region
                cv2.rectangle(display_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(display_image, str(i+1), (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Desenhar seleção atual
            if self.current_selection:
                (x1, y1), (x2, y2) = self.current_selection
                cv2.rectangle(display_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            
            # Criar máscara com faixa HSV atual
            lower_hsv, upper_hsv = self.get_current_hsv_range()
            mask = self.create_mask(lower_hsv, upper_hsv)
            
            # Aplicar máscara
            masked_image = cv2.bitwise_and(self.image, self.image, mask=mask)
            
            # Combinar imagens
            combined = np.hstack([display_image, masked_image])
            
            # Redimensionar se muito grande
            height, width = combined.shape[:2]
            if width > 1600:
                scale = 1600 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                combined = cv2.resize(combined, (new_width, new_height))
            
            cv2.imshow('Calibração de Cores', combined)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Salvar faixa atual
                range_data = {
                    'lower_hsv': lower_hsv,
                    'upper_hsv': upper_hsv,
                    'description': f'Range_{len(saved_ranges)+1}'
                }
                saved_ranges.append(range_data)
                print(f"Faixa salva: {lower_hsv} - {upper_hsv}")
            elif key == ord('r'):
                # Resetar seleções
                self.selected_regions = []
                print("Seleções resetadas")
        
        cv2.destroyAllWindows()
        
        # Salvar configuração final
        if saved_ranges:
            self.save_config(saved_ranges)
        
        return saved_ranges
    
    def save_config(self, ranges):
        """Salva as faixas calibradas em um arquivo"""
        config_data = {
            'functional_led_hsv_ranges': [],
            'defective_led_hsv_ranges': []
        }
        
        print("\nFaixas calibradas:")
        for i, range_data in enumerate(ranges):
            lower = range_data['lower_hsv']
            upper = range_data['upper_hsv']
            print(f"{i+1}. {lower} - {upper}")
            
            # Perguntar ao usuário o tipo de LED
            while True:
                led_type = input(f"Esta faixa é para LED funcional (f) ou defeituoso (d)? ").lower()
                if led_type in ['f', 'funcional']:
                    config_data['functional_led_hsv_ranges'].append([lower, upper])
                    break
                elif led_type in ['d', 'defeituoso']:
                    config_data['defective_led_hsv_ranges'].append([lower, upper])
                    break
                else:
                    print("Digite 'f' para funcional ou 'd' para defeituoso")
        
        # Salvar arquivo
        output_file = 'color_calibration.json'
        with open(output_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"\nConfiguração salva em: {output_file}")
        print("Você pode usar esta configuração no arquivo config.py")

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Calibra faixas de cores para detecção de LEDs"
    )
    
    parser.add_argument(
        "image_path",
        help="Caminho para a imagem de calibração"
    )
    
    args = parser.parse_args()
    
    setup_logging()
    
    # Verificar se arquivo existe
    if not Path(args.image_path).exists():
        print(f"Erro: Arquivo não encontrado: {args.image_path}")
        return 1
    
    try:
        calibrator = ColorCalibrator(args.image_path)
        calibrator.run()
        return 0
    except Exception as e:
        logging.error(f"Erro durante a calibração: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
