"""
Configurações do Bot de Marcação Automática de LEDs no CVAT
"""
import os
from typing import Dict, List, Tuple
from pydantic import BaseModel
from dotenv import load_dotenv

load_dotenv()

class CVATConfig(BaseModel):
    """Configurações de conexão com o CVAT"""
    host: str = os.getenv("CVAT_HOST", "http://localhost:8080")
    username: str = os.getenv("CVAT_USERNAME", "")
    password: str = os.getenv("CVAT_PASSWORD", "")
    
class LEDDetectionConfig(BaseModel):
    """Configurações para detecção de LEDs"""
    # Parâmetros para detecção de contornos
    min_contour_area: int = 50
    max_contour_area: int = 2000
    
    # Parâmetros para filtros de forma
    min_aspect_ratio: float = 0.3
    max_aspect_ratio: float = 3.0
    min_extent: float = 0.3  # Área do contorno / área do retângulo delimitador
    
    # Parâmetros para detecção de círculos (LEDs redondos)
    enable_circle_detection: bool = True
    min_radius: int = 5
    max_radius: int = 50
    circle_param1: int = 50
    circle_param2: int = 30
    
class ColorAnalysisConfig(BaseModel):
    """Configurações para análise de cores dos LEDs"""
    # Faixas de cor para LEDs funcionais (verde) em HSV
    functional_led_hsv_ranges: List[Tuple[Tuple[int, int, int], Tuple[int, int, int]]] = [
        ((40, 50, 50), (80, 255, 255)),  # Verde principal
        ((35, 30, 30), (85, 255, 255)),  # Verde mais amplo
    ]
    
    # Faixas de cor para LEDs com defeito (vermelho) em HSV
    defective_led_hsv_ranges: List[Tuple[Tuple[int, int, int], Tuple[int, int, int]]] = [
        ((0, 50, 50), (10, 255, 255)),   # Vermelho baixo
        ((170, 50, 50), (180, 255, 255)), # Vermelho alto
    ]
    
    # Limiar de confiança para classificação de cor
    color_confidence_threshold: float = 0.6
    
    # Tamanho da região de análise ao redor do LED detectado
    analysis_region_padding: int = 5

class AnnotationConfig(BaseModel):
    """Configurações para as anotações no CVAT"""
    # Labels que serão criados/usados no CVAT
    functional_led_label: str = "LED_FUNCIONAL"
    defective_led_label: str = "LED_COM_DEFEITO"
    
    # Cores das anotações (RGB)
    functional_led_color: str = "#00FF00"  # Verde
    defective_led_color: str = "#FF0000"   # Vermelho
    
    # Limiar de confiança para criar anotações
    annotation_confidence_threshold: float = 0.7
    
    # Remover anotações existentes antes de adicionar novas
    clear_existing_annotations: bool = True

class BotConfig(BaseModel):
    """Configuração principal do bot"""
    cvat: CVATConfig = CVATConfig()
    led_detection: LEDDetectionConfig = LEDDetectionConfig()
    color_analysis: ColorAnalysisConfig = ColorAnalysisConfig()
    annotation: AnnotationConfig = AnnotationConfig()
    
    # Configurações gerais
    debug_mode: bool = os.getenv("DEBUG", "false").lower() == "true"
    save_debug_images: bool = False
    debug_output_dir: str = "debug_output"
    
    # Configurações de processamento
    batch_size: int = 10
    max_workers: int = 4

# Instância global de configuração
config = BotConfig()

def load_config_from_file(config_path: str) -> BotConfig:
    """Carrega configuração de um arquivo JSON"""
    import json
    with open(config_path, 'r') as f:
        config_data = json.load(f)
    return BotConfig(**config_data)

def save_config_to_file(config: BotConfig, config_path: str):
    """Salva configuração em um arquivo JSON"""
    import json
    with open(config_path, 'w') as f:
        json.dump(config.model_dump(), f, indent=2)
