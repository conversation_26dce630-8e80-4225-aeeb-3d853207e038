"""
Detector de LEDs em imagens de placas eletrônicas
"""
import cv2
import numpy as np
from typing import List, Tuple, Optional
import logging
from config import LEDDetectionConfig

logger = logging.getLogger(__name__)

class LEDDetector:
    """Classe para detectar LEDs em imagens de placas eletrônicas"""
    
    def __init__(self, config: LEDDetectionConfig):
        self.config = config
        
    def detect_leds(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        Detecta LEDs na imagem e retorna suas coordenadas
        
        Args:
            image: Imagem BGR da placa eletrônica
            
        Returns:
            Lista de bounding boxes (x, y, w, h) dos LEDs detectados
        """
        # Converter para escala de cinza
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Aplicar filtros para melhorar a detecção
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Detectar LEDs usando múltiplas abordagens
        contour_detections = self._detect_by_contours(blurred, image)
        circle_detections = []
        
        if self.config.enable_circle_detection:
            circle_detections = self._detect_by_circles(blurred, image)
        
        # Combinar e filtrar detecções
        all_detections = contour_detections + circle_detections
        filtered_detections = self._filter_overlapping_detections(all_detections)
        
        logger.info(f"Detectados {len(filtered_detections)} LEDs na imagem")
        return filtered_detections
    
    def _detect_by_contours(self, gray: np.ndarray, original: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detecta LEDs usando análise de contornos"""
        detections = []
        
        # Aplicar threshold adaptativo
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Encontrar contornos
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Filtrar por área
            if not (self.config.min_contour_area <= area <= self.config.max_contour_area):
                continue
            
            # Obter bounding box
            x, y, w, h = cv2.boundingRect(contour)
            
            # Filtrar por aspect ratio
            aspect_ratio = w / h if h > 0 else 0
            if not (self.config.min_aspect_ratio <= aspect_ratio <= self.config.max_aspect_ratio):
                continue
            
            # Filtrar por extent (preenchimento do bounding box)
            extent = area / (w * h) if (w * h) > 0 else 0
            if extent < self.config.min_extent:
                continue
            
            # Verificar se a região tem características de LED
            if self._is_led_like_region(original[y:y+h, x:x+w]):
                detections.append((x, y, w, h))
        
        return detections
    
    def _detect_by_circles(self, gray: np.ndarray, original: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detecta LEDs circulares usando HoughCircles"""
        detections = []
        
        # Aplicar HoughCircles
        circles = cv2.HoughCircles(
            gray,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=30,
            param1=self.config.circle_param1,
            param2=self.config.circle_param2,
            minRadius=self.config.min_radius,
            maxRadius=self.config.max_radius
        )
        
        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            
            for (x, y, r) in circles:
                # Converter círculo para bounding box
                bbox_x = max(0, x - r)
                bbox_y = max(0, y - r)
                bbox_w = min(original.shape[1] - bbox_x, 2 * r)
                bbox_h = min(original.shape[0] - bbox_y, 2 * r)
                
                # Verificar se a região tem características de LED
                region = original[bbox_y:bbox_y+bbox_h, bbox_x:bbox_x+bbox_w]
                if self._is_led_like_region(region):
                    detections.append((bbox_x, bbox_y, bbox_w, bbox_h))
        
        return detections
    
    def _is_led_like_region(self, region: np.ndarray) -> bool:
        """
        Verifica se uma região tem características típicas de um LED
        
        Args:
            region: Região da imagem a ser analisada
            
        Returns:
            True se a região parece ser um LED
        """
        if region.size == 0:
            return False
        
        # Converter para HSV para análise de cor
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        
        # Verificar se há cores saturadas (típicas de LEDs acesos)
        saturation = hsv[:, :, 1]
        value = hsv[:, :, 2]
        
        # LEDs acesos tendem a ter alta saturação e valor
        high_saturation_pixels = np.sum(saturation > 100)
        high_value_pixels = np.sum(value > 150)
        total_pixels = region.shape[0] * region.shape[1]
        
        # Pelo menos 30% dos pixels devem ter alta saturação ou valor
        return (high_saturation_pixels + high_value_pixels) / total_pixels > 0.3
    
    def _filter_overlapping_detections(self, detections: List[Tuple[int, int, int, int]]) -> List[Tuple[int, int, int, int]]:
        """Remove detecções sobrepostas usando Non-Maximum Suppression"""
        if not detections:
            return []
        
        # Converter para formato necessário para NMS
        boxes = np.array(detections)
        scores = np.ones(len(detections))  # Assumir score igual para todas
        
        # Aplicar NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), scores.tolist(), 0.5, 0.4
        )
        
        if len(indices) > 0:
            return [detections[i] for i in indices.flatten()]
        else:
            return []
    
    def visualize_detections(self, image: np.ndarray, detections: List[Tuple[int, int, int, int]]) -> np.ndarray:
        """
        Visualiza as detecções na imagem
        
        Args:
            image: Imagem original
            detections: Lista de bounding boxes detectados
            
        Returns:
            Imagem com as detecções desenhadas
        """
        result = image.copy()
        
        for i, (x, y, w, h) in enumerate(detections):
            # Desenhar bounding box
            cv2.rectangle(result, (x, y), (x + w, y + h), (255, 0, 0), 2)
            
            # Adicionar número da detecção
            cv2.putText(result, str(i), (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        return result
