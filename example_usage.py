#!/usr/bin/env python3
"""
Exemplo de uso do CVAT LED Bot via código Python
"""
import logging
from config import BotConfig, CVATConfig, LEDDetectionConfig, ColorAnalysisConfig, AnnotationConfig
from cvat_bot import CVATLEDBot

def setup_logging():
    """Configura logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def example_basic_usage():
    """Exemplo básico de uso"""
    print("=== Exemplo Básico ===")
    
    # Usar configuração padrão
    with CVATLEDBot(BotConfig()) as bot:
        # Anotar tarefa específica
        stats = bot.annotate_task(task_id=12345)
        print(f"Processamento concluído: {stats}")

def example_custom_config():
    """Exemplo com configuração customizada"""
    print("=== Exemplo com Configuração Customizada ===")
    
    # Criar configuração customizada
    custom_config = BotConfig(
        cvat=CVATConfig(
            host="http://seu-cvat-server.com",
            username="seu_usuario",
            password="sua_senha"
        ),
        led_detection=LEDDetectionConfig(
            min_contour_area=30,  # LEDs menores
            max_contour_area=1500,
            enable_circle_detection=True,
            min_radius=3,
            max_radius=40
        ),
        color_analysis=ColorAnalysisConfig(
            # Faixas de cor mais específicas
            functional_led_hsv_ranges=[
                ((45, 60, 60), (75, 255, 255)),  # Verde mais restrito
            ],
            defective_led_hsv_ranges=[
                ((0, 60, 60), (8, 255, 255)),    # Vermelho mais restrito
                ((172, 60, 60), (180, 255, 255))
            ],
            color_confidence_threshold=0.8  # Maior confiança necessária
        ),
        annotation=AnnotationConfig(
            functional_led_label="LED_OK",
            defective_led_label="LED_FALHA",
            annotation_confidence_threshold=0.8,
            clear_existing_annotations=False  # Não limpar anotações existentes
        ),
        debug_mode=True,
        save_debug_images=True
    )
    
    with CVATLEDBot(custom_config) as bot:
        stats = bot.annotate_task(task_id=12345)
        print(f"Processamento concluído: {stats}")

def example_batch_processing():
    """Exemplo de processamento em lote"""
    print("=== Exemplo de Processamento em Lote ===")
    
    task_ids = [12345, 12346, 12347]  # Lista de tarefas para processar
    
    with CVATLEDBot(BotConfig()) as bot:
        total_stats = {
            'total_tasks': len(task_ids),
            'successful_tasks': 0,
            'failed_tasks': 0,
            'total_annotations': 0,
            'total_functional_leds': 0,
            'total_defective_leds': 0
        }
        
        for task_id in task_ids:
            try:
                print(f"Processando tarefa {task_id}...")
                stats = bot.annotate_task(task_id)
                
                total_stats['successful_tasks'] += 1
                total_stats['total_annotations'] += stats['total_annotations']
                total_stats['total_functional_leds'] += stats['functional_leds']
                total_stats['total_defective_leds'] += stats['defective_leds']
                
                print(f"Tarefa {task_id} concluída: {stats['total_annotations']} anotações")
                
            except Exception as e:
                print(f"Erro ao processar tarefa {task_id}: {e}")
                total_stats['failed_tasks'] += 1
        
        print("\n=== Estatísticas Finais ===")
        print(f"Tarefas processadas: {total_stats['successful_tasks']}/{total_stats['total_tasks']}")
        print(f"Total de anotações: {total_stats['total_annotations']}")
        print(f"LEDs funcionais: {total_stats['total_functional_leds']}")
        print(f"LEDs com defeito: {total_stats['total_defective_leds']}")

def example_with_error_handling():
    """Exemplo com tratamento de erros robusto"""
    print("=== Exemplo com Tratamento de Erros ===")
    
    try:
        config = BotConfig()
        
        # Validar configuração antes de usar
        if not config.cvat.username or not config.cvat.password:
            raise ValueError("Credenciais do CVAT não configuradas")
        
        with CVATLEDBot(config) as bot:
            # Tentar conectar primeiro
            print("Testando conexão com CVAT...")
            
            # Processar tarefa
            task_id = 12345
            stats = bot.annotate_task(task_id)
            
            # Verificar se houve anotações criadas
            if stats['total_annotations'] == 0:
                print("⚠️  Nenhuma anotação foi criada. Verifique:")
                print("   - Se a imagem contém LEDs visíveis")
                print("   - Se as configurações de detecção estão adequadas")
                print("   - Se as faixas de cor estão corretas")
            else:
                print(f"✅ Sucesso! {stats['total_annotations']} anotações criadas")
    
    except ValueError as e:
        print(f"❌ Erro de configuração: {e}")
    except ConnectionError as e:
        print(f"❌ Erro de conexão: {e}")
        print("   Verifique se o CVAT está acessível e as credenciais estão corretas")
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        logging.exception("Detalhes do erro:")

def main():
    """Função principal com exemplos"""
    setup_logging()
    
    print("🤖 CVAT LED Bot - Exemplos de Uso")
    print("="*50)
    
    # Escolher qual exemplo executar
    examples = {
        '1': ("Uso Básico", example_basic_usage),
        '2': ("Configuração Customizada", example_custom_config),
        '3': ("Processamento em Lote", example_batch_processing),
        '4': ("Tratamento de Erros", example_with_error_handling)
    }
    
    print("\nExemplos disponíveis:")
    for key, (name, _) in examples.items():
        print(f"  {key}. {name}")
    
    choice = input("\nEscolha um exemplo (1-4) ou 'all' para todos: ").strip()
    
    if choice.lower() == 'all':
        for name, func in examples.values():
            print(f"\n{'='*20} {name} {'='*20}")
            try:
                func()
            except Exception as e:
                print(f"Erro no exemplo: {e}")
    elif choice in examples:
        name, func = examples[choice]
        print(f"\n{'='*20} {name} {'='*20}")
        func()
    else:
        print("Opção inválida")

if __name__ == "__main__":
    main()
