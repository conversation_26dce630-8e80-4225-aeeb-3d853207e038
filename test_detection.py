#!/usr/bin/env python3
"""
Script para testar a detecção de LEDs em uma imagem local
"""
import argparse
import cv2
import numpy as np
from pathlib import Path
import logging

from config import config
from led_detector import LEDDetector
from color_analyzer import ColorAnalyzer, LEDStatus

def setup_logging():
    """Configura logging básico"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_detection(image_path: str, output_path: str = None):
    """
    Testa a detecção de LEDs em uma imagem
    
    Args:
        image_path: Caminho para a imagem de teste
        output_path: Caminho para salvar a imagem com detecções (opcional)
    """
    logger = logging.getLogger(__name__)
    
    # Carregar imagem
    image = cv2.imread(image_path)
    if image is None:
        logger.error(f"Não foi possível carregar a imagem: {image_path}")
        return
    
    logger.info(f"Imagem carregada: {image.shape}")
    
    # Inicializar detectores
    led_detector = LEDDetector(config.led_detection)
    color_analyzer = ColorAnalyzer(config.color_analysis)
    
    # Detectar LEDs
    logger.info("Detectando LEDs...")
    detections = led_detector.detect_leds(image)
    logger.info(f"Encontrados {len(detections)} LEDs")
    
    # Analisar cores e criar visualização
    result_image = image.copy()
    stats = {
        'total': len(detections),
        'functional': 0,
        'defective': 0,
        'unknown': 0
    }
    
    for i, bbox in enumerate(detections):
        # Analisar cor
        status, confidence = color_analyzer.analyze_led_color(image, bbox)
        
        # Atualizar estatísticas
        if status == LEDStatus.FUNCTIONAL:
            stats['functional'] += 1
        elif status == LEDStatus.DEFECTIVE:
            stats['defective'] += 1
        else:
            stats['unknown'] += 1
        
        # Visualizar resultado
        result_image = color_analyzer.visualize_color_analysis(
            result_image, bbox, status, confidence
        )
        
        logger.info(f"LED {i+1}: {status.value} (confiança: {confidence:.2f})")
    
    # Exibir estatísticas
    print("\n" + "="*40)
    print("ESTATÍSTICAS DE DETECÇÃO")
    print("="*40)
    print(f"Total de LEDs detectados: {stats['total']}")
    print(f"LEDs funcionais (verde): {stats['functional']}")
    print(f"LEDs com defeito (vermelho): {stats['defective']}")
    print(f"LEDs desconhecidos: {stats['unknown']}")
    print("="*40)
    
    # Salvar ou exibir resultado
    if output_path:
        cv2.imwrite(output_path, result_image)
        logger.info(f"Resultado salvo em: {output_path}")
    else:
        # Redimensionar para exibição se muito grande
        height, width = result_image.shape[:2]
        if height > 800 or width > 1200:
            scale = min(800/height, 1200/width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            result_image = cv2.resize(result_image, (new_width, new_height))
        
        cv2.imshow('Detecção de LEDs', result_image)
        print("\nPressione qualquer tecla para fechar...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Testa detecção de LEDs em uma imagem local"
    )
    
    parser.add_argument(
        "image_path",
        help="Caminho para a imagem de teste"
    )
    
    parser.add_argument(
        "--output",
        "-o",
        help="Caminho para salvar a imagem com detecções"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Ativar modo debug"
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    setup_logging()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Verificar se arquivo existe
    if not Path(args.image_path).exists():
        print(f"Erro: Arquivo não encontrado: {args.image_path}")
        return 1
    
    try:
        test_detection(args.image_path, args.output)
        return 0
    except Exception as e:
        logging.error(f"Erro durante o teste: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
