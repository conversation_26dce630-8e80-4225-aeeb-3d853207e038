"""
Bot para integração com CVAT e marcação automática de LEDs
"""
import logging
from typing import List, Dict, Any, Optional
import numpy as np
from PIL import Image
import io

import cvat_sdk
from cvat_sdk import make_client
import cvat_sdk.models as models
import cvat_sdk.auto_annotation as cvataa

from config import BotConfig
from led_detector import LEDDetector
from color_analyzer import ColorAnalyzer, LEDStatus

logger = logging.getLogger(__name__)

class CVATLEDBot:
    """Bot principal para marcação automática de LEDs no CVAT"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.led_detector = LEDDetector(config.led_detection)
        self.color_analyzer = ColorAnalyzer(config.color_analysis)
        self.client = None
        
    def connect(self):
        """Conecta ao servidor CVAT"""
        try:
            self.client = make_client(
                host=self.config.cvat.host,
                credentials=(self.config.cvat.username, self.config.cvat.password)
            )
            logger.info(f"Conectado ao CVAT em {self.config.cvat.host}")
        except Exception as e:
            logger.error(f"Erro ao conectar ao CVAT: {e}")
            raise
    
    def disconnect(self):
        """Desconecta do servidor CVAT"""
        if self.client:
            self.client.close()
            self.client = None
            logger.info("Desconectado do CVAT")
    
    def annotate_task(self, task_id: int) -> Dict[str, Any]:
        """
        Anota uma tarefa específica no CVAT
        
        Args:
            task_id: ID da tarefa no CVAT
            
        Returns:
            Dicionário com estatísticas da anotação
        """
        if not self.client:
            raise RuntimeError("Cliente CVAT não conectado. Chame connect() primeiro.")
        
        logger.info(f"Iniciando anotação da tarefa {task_id}")
        
        try:
            # Obter informações da tarefa
            task = self.client.tasks.retrieve(task_id)
            logger.info(f"Tarefa encontrada: {task.name}")
            
            # Verificar/criar labels necessários
            self._ensure_labels_exist(task)
            
            # Processar cada job da tarefa
            stats = {
                'task_id': task_id,
                'total_jobs': len(task.jobs),
                'processed_jobs': 0,
                'total_annotations': 0,
                'functional_leds': 0,
                'defective_leds': 0,
                'unknown_leds': 0
            }
            
            for job in task.jobs:
                job_stats = self._process_job(job)
                stats['processed_jobs'] += 1
                stats['total_annotations'] += job_stats['total_annotations']
                stats['functional_leds'] += job_stats['functional_leds']
                stats['defective_leds'] += job_stats['defective_leds']
                stats['unknown_leds'] += job_stats['unknown_leds']
            
            logger.info(f"Anotação concluída. Estatísticas: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Erro ao anotar tarefa {task_id}: {e}")
            raise
    
    def _ensure_labels_exist(self, task):
        """Garante que os labels necessários existem na tarefa"""
        existing_labels = {label.name: label for label in task.labels}
        
        required_labels = [
            self.config.annotation.functional_led_label,
            self.config.annotation.defective_led_label
        ]
        
        for label_name in required_labels:
            if label_name not in existing_labels:
                logger.warning(f"Label '{label_name}' não encontrado na tarefa. "
                             f"Certifique-se de que existe no projeto.")
    
    def _process_job(self, job) -> Dict[str, Any]:
        """
        Processa um job específico
        
        Args:
            job: Objeto job do CVAT
            
        Returns:
            Estatísticas do processamento
        """
        logger.info(f"Processando job {job.id}")
        
        stats = {
            'job_id': job.id,
            'total_annotations': 0,
            'functional_leds': 0,
            'defective_leds': 0,
            'unknown_leds': 0
        }
        
        # Limpar anotações existentes se configurado
        if self.config.annotation.clear_existing_annotations:
            self._clear_job_annotations(job)
        
        # Processar cada frame do job
        for frame_id in range(job.start_frame, job.stop_frame + 1):
            frame_stats = self._process_frame(job, frame_id)
            stats['total_annotations'] += frame_stats['total_annotations']
            stats['functional_leds'] += frame_stats['functional_leds']
            stats['defective_leds'] += frame_stats['defective_leds']
            stats['unknown_leds'] += frame_stats['unknown_leds']
        
        return stats
    
    def _process_frame(self, job, frame_id: int) -> Dict[str, Any]:
        """
        Processa um frame específico
        
        Args:
            job: Objeto job do CVAT
            frame_id: ID do frame
            
        Returns:
            Estatísticas do frame
        """
        stats = {
            'frame_id': frame_id,
            'total_annotations': 0,
            'functional_leds': 0,
            'defective_leds': 0,
            'unknown_leds': 0
        }
        
        try:
            # Obter imagem do frame
            image_data = job.get_frame(frame_id)
            image = Image.open(io.BytesIO(image_data))
            image_np = np.array(image)
            
            # Detectar LEDs
            led_detections = self.led_detector.detect_leds(image_np)
            
            # Analisar cada LED detectado
            annotations = []
            for bbox in led_detections:
                status, confidence = self.color_analyzer.analyze_led_color(image_np, bbox)
                
                if confidence >= self.config.annotation.annotation_confidence_threshold:
                    annotation = self._create_annotation(bbox, status, confidence, frame_id)
                    if annotation:
                        annotations.append(annotation)
                        stats['total_annotations'] += 1
                        
                        if status == LEDStatus.FUNCTIONAL:
                            stats['functional_leds'] += 1
                        elif status == LEDStatus.DEFECTIVE:
                            stats['defective_leds'] += 1
                        else:
                            stats['unknown_leds'] += 1
            
            # Enviar anotações para o CVAT
            if annotations:
                self._upload_annotations(job, annotations)
                
        except Exception as e:
            logger.error(f"Erro ao processar frame {frame_id}: {e}")
        
        return stats
    
    def _create_annotation(self, bbox, status: LEDStatus, confidence: float, frame_id: int) -> Optional[models.LabeledShapeRequest]:
        """
        Cria uma anotação para um LED detectado
        
        Args:
            bbox: Bounding box do LED (x, y, w, h)
            status: Status do LED
            confidence: Confiança da detecção
            frame_id: ID do frame
            
        Returns:
            Objeto de anotação ou None se não for possível criar
        """
        x, y, w, h = bbox
        
        # Determinar label baseado no status
        if status == LEDStatus.FUNCTIONAL:
            label_name = self.config.annotation.functional_led_label
        elif status == LEDStatus.DEFECTIVE:
            label_name = self.config.annotation.defective_led_label
        else:
            return None  # Não criar anotação para status desconhecido
        
        # Criar pontos do retângulo (formato CVAT: x1,y1,x2,y2)
        points = [x, y, x + w, y + h]
        
        try:
            annotation = models.LabeledShapeRequest(
                type="rectangle",
                frame=frame_id,
                label_id=None,  # Será definido durante o upload
                points=points,
                attributes=[]
            )
            
            # Adicionar informações customizadas como atributos se necessário
            # annotation.attributes.append(
            #     models.AttributeValRequest(
            #         spec_id=confidence_attr_id,
            #         value=str(round(confidence, 2))
            #     )
            # )
            
            return annotation
            
        except Exception as e:
            logger.error(f"Erro ao criar anotação: {e}")
            return None
    
    def _upload_annotations(self, job, annotations: List[models.LabeledShapeRequest]):
        """
        Envia anotações para o CVAT
        
        Args:
            job: Objeto job do CVAT
            annotations: Lista de anotações
        """
        try:
            # Obter labels da tarefa para mapear nomes para IDs
            task = self.client.tasks.retrieve(job.task_id)
            label_map = {label.name: label.id for label in task.labels}
            
            # Mapear label_ids nas anotações
            for annotation in annotations:
                if annotation.type == "rectangle":
                    # Determinar label baseado na posição ou outros critérios
                    # Por simplicidade, vamos assumir que temos uma forma de determinar
                    # o label correto para cada anotação
                    pass
            
            # Criar patch de anotações
            annotation_patch = models.PatchedLabeledDataRequest(
                shapes=annotations
            )
            
            # Enviar para o CVAT
            job.update_annotations(annotation_patch)
            logger.info(f"Enviadas {len(annotations)} anotações para o job {job.id}")
            
        except Exception as e:
            logger.error(f"Erro ao enviar anotações: {e}")
            raise
    
    def _clear_job_annotations(self, job):
        """Remove todas as anotações existentes de um job"""
        try:
            # Obter anotações existentes
            annotations = job.get_annotations()
            
            # Limpar shapes
            clear_patch = models.PatchedLabeledDataRequest(
                shapes=[],
                tracks=[],
                tags=[]
            )
            
            job.update_annotations(clear_patch)
            logger.info(f"Anotações removidas do job {job.id}")
            
        except Exception as e:
            logger.error(f"Erro ao limpar anotações do job {job.id}: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
