{"cvat": {"host": "http://localhost:8080", "username": "admin", "password": "password"}, "led_detection": {"min_contour_area": 50, "max_contour_area": 2000, "min_aspect_ratio": 0.3, "max_aspect_ratio": 3.0, "min_extent": 0.3, "enable_circle_detection": true, "min_radius": 5, "max_radius": 50, "circle_param1": 50, "circle_param2": 30}, "color_analysis": {"functional_led_hsv_ranges": [[[40, 50, 50], [80, 255, 255]], [[35, 30, 30], [85, 255, 255]]], "defective_led_hsv_ranges": [[[0, 50, 50], [10, 255, 255]], [[170, 50, 50], [180, 255, 255]]], "color_confidence_threshold": 0.6, "analysis_region_padding": 5}, "annotation": {"functional_led_label": "LED_FUNCIONAL", "defective_led_label": "LED_COM_DEFEITO", "functional_led_color": "#00FF00", "defective_led_color": "#FF0000", "annotation_confidence_threshold": 0.7, "clear_existing_annotations": true}, "debug_mode": false, "save_debug_images": false, "debug_output_dir": "debug_output", "batch_size": 10, "max_workers": 4}