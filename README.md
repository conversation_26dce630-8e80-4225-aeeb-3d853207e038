# CVAT LED Bot - Marcação Automática de LEDs

Bot para marcação automática de LEDs funcionais e com defeito em imagens de placas eletrônicas no CVAT.

## Características

- **Detecção Automática**: Detecta LEDs em imagens de placas eletrônicas usando análise de contornos e detecção de círculos
- **Classificação por Cor**: Classifica LEDs como funcionais (verde) ou com defeito (vermelho) baseado na análise de cor
- **Integração CVAT**: Integração completa com o CVAT usando o Python SDK oficial
- **Configuração Flexível**: Sistema de configuração abrangente para ajustar parâmetros de detecção
- **Modo Debug**: Suporte para debug com salvamento de imagens processadas

## Instalação

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd cvat-led-bot
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

## Configuração

### Variáveis de Ambiente

Crie um arquivo `.env` baseado no `.env.example`:

```env
CVAT_HOST=http://localhost:8080
CVAT_USERNAME=seu_usuario
CVAT_PASSWORD=sua_senha
DEBUG=false
```

### Configuração Avançada

Para configurações mais avançadas, você pode criar um arquivo JSON de configuração:

```json
{
  "cvat": {
    "host": "http://localhost:8080",
    "username": "usuario",
    "password": "senha"
  },
  "led_detection": {
    "min_contour_area": 50,
    "max_contour_area": 2000,
    "enable_circle_detection": true,
    "min_radius": 5,
    "max_radius": 50
  },
  "color_analysis": {
    "functional_led_hsv_ranges": [
      [[40, 50, 50], [80, 255, 255]]
    ],
    "defective_led_hsv_ranges": [
      [[0, 50, 50], [10, 255, 255]],
      [[170, 50, 50], [180, 255, 255]]
    ],
    "color_confidence_threshold": 0.6
  },
  "annotation": {
    "functional_led_label": "LED_FUNCIONAL",
    "defective_led_label": "LED_COM_DEFEITO",
    "annotation_confidence_threshold": 0.7,
    "clear_existing_annotations": true
  }
}
```

## Uso

### Uso Básico

```bash
python main.py <TASK_ID>
```

### Opções Avançadas

```bash
# Usar arquivo de configuração personalizado
python main.py <TASK_ID> --config config.json

# Modo debug
python main.py <TASK_ID> --debug

# Salvar imagens de debug
python main.py <TASK_ID> --save-debug-images

# Modo dry-run (não faz alterações)
python main.py <TASK_ID> --dry-run
```

## Preparação do CVAT

Antes de usar o bot, certifique-se de que:

1. **Projeto configurado**: Crie um projeto no CVAT com os labels necessários:
   - `LED_FUNCIONAL` (ou o nome configurado)
   - `LED_COM_DEFEITO` (ou o nome configurado)

2. **Tarefa criada**: Crie uma tarefa no projeto com as imagens das placas eletrônicas

3. **Permissões**: Certifique-se de que o usuário tem permissões para editar anotações na tarefa

## Como Funciona

### 1. Detecção de LEDs

O bot usa duas abordagens para detectar LEDs:

- **Análise de Contornos**: Detecta formas que podem ser LEDs baseado em área, aspect ratio e preenchimento
- **Detecção de Círculos**: Usa HoughCircles para detectar LEDs circulares

### 2. Classificação por Cor

Para cada LED detectado:

- Extrai a região do LED com padding configurável
- Converte para espaço de cor HSV
- Compara com faixas de cor predefinidas para verde (funcional) e vermelho (defeito)
- Calcula confiança baseada na porcentagem de pixels que correspondem

### 3. Criação de Anotações

- Cria retângulos delimitadores para LEDs com confiança acima do limiar
- Atribui o label apropriado baseado na classificação de cor
- Envia as anotações para o CVAT via API

## Personalização

### Ajustar Detecção de LEDs

Modifique os parâmetros em `config.py` ou arquivo de configuração:

```python
led_detection:
  min_contour_area: 50      # Área mínima do contorno
  max_contour_area: 2000    # Área máxima do contorno
  min_aspect_ratio: 0.3     # Aspect ratio mínimo
  max_aspect_ratio: 3.0     # Aspect ratio máximo
  min_extent: 0.3           # Preenchimento mínimo do bounding box
```

### Ajustar Análise de Cor

Modifique as faixas de cor HSV:

```python
color_analysis:
  functional_led_hsv_ranges:  # Verde
    - [[40, 50, 50], [80, 255, 255]]
  defective_led_hsv_ranges:   # Vermelho
    - [[0, 50, 50], [10, 255, 255]]
    - [[170, 50, 50], [180, 255, 255]]
```

## Troubleshooting

### Problemas Comuns

1. **Erro de conexão com CVAT**:
   - Verifique se o host, usuário e senha estão corretos
   - Certifique-se de que o CVAT está acessível

2. **LEDs não detectados**:
   - Ajuste os parâmetros de detecção (área, aspect ratio)
   - Verifique se as imagens têm qualidade suficiente
   - Use modo debug para visualizar as detecções

3. **Classificação de cor incorreta**:
   - Ajuste as faixas de cor HSV
   - Modifique o limiar de confiança
   - Verifique a iluminação das imagens

### Debug

Use o modo debug para investigar problemas:

```bash
python main.py <TASK_ID> --debug --save-debug-images
```

Isso salvará imagens com as detecções visualizadas no diretório `debug_output/`.

## Estrutura do Projeto

```
cvat-led-bot/
├── main.py              # Script principal
├── config.py            # Configurações
├── cvat_bot.py          # Integração com CVAT
├── led_detector.py      # Detecção de LEDs
├── color_analyzer.py    # Análise de cores
├── requirements.txt     # Dependências
├── .env.example         # Exemplo de configuração
└── README.md           # Documentação
```

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a MIT License - veja o arquivo LICENSE para detalhes.
