#!/usr/bin/env python3
"""
Script de setup para o CVAT LED Bot
"""
import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Executa um comando e exibe o resultado"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Concluído")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Erro:")
        print(f"   {e.stderr}")
        return False

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9 ou superior é necessário")
        print(f"   Versão atual: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatível")
    return True

def create_virtual_environment():
    """Cria ambiente virtual se não existir"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Ambiente virtual já existe")
        return True
    
    return run_command("python -m venv venv", "Criando ambiente virtual")

def install_dependencies():
    """Instala dependências"""
    # Determinar comando pip baseado no sistema
    if sys.platform == "win32":
        pip_cmd = "venv\\Scripts\\pip"
    else:
        pip_cmd = "venv/bin/pip"
    
    commands = [
        (f"{pip_cmd} install --upgrade pip", "Atualizando pip"),
        (f"{pip_cmd} install -r requirements.txt", "Instalando dependências")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def create_env_file():
    """Cria arquivo .env se não existir"""
    env_path = Path(".env")
    if env_path.exists():
        print("✅ Arquivo .env já existe")
        return True
    
    try:
        # Copiar do exemplo
        with open(".env.example", "r") as src:
            content = src.read()
        
        with open(".env", "w") as dst:
            dst.write(content)
        
        print("✅ Arquivo .env criado a partir do exemplo")
        print("⚠️  IMPORTANTE: Edite o arquivo .env com suas configurações do CVAT")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar arquivo .env: {e}")
        return False

def create_directories():
    """Cria diretórios necessários"""
    directories = ["debug_output", "examples"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Diretórios criados")
    return True

def show_next_steps():
    """Exibe próximos passos"""
    print("\n" + "="*60)
    print("🎉 INSTALAÇÃO CONCLUÍDA!")
    print("="*60)
    print("\n📋 PRÓXIMOS PASSOS:")
    print("\n1. Configure suas credenciais do CVAT:")
    print("   - Edite o arquivo .env")
    print("   - Defina CVAT_HOST, CVAT_USERNAME e CVAT_PASSWORD")
    
    print("\n2. Teste a detecção em uma imagem local:")
    print("   python test_detection.py caminho/para/imagem.jpg")
    
    print("\n3. Calibre as cores dos LEDs (opcional):")
    print("   python calibrate_colors.py caminho/para/imagem.jpg")
    
    print("\n4. Execute o bot em uma tarefa do CVAT:")
    print("   python main.py <TASK_ID>")
    
    print("\n📚 DOCUMENTAÇÃO:")
    print("   - Leia o README.md para instruções detalhadas")
    print("   - Veja config_example.json para configurações avançadas")
    
    print("\n🔧 ATIVAÇÃO DO AMBIENTE VIRTUAL:")
    if sys.platform == "win32":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("\n" + "="*60)

def main():
    """Função principal do setup"""
    print("🚀 CVAT LED Bot - Setup")
    print("="*40)
    
    # Verificar versão do Python
    if not check_python_version():
        return 1
    
    # Criar ambiente virtual
    if not create_virtual_environment():
        return 1
    
    # Instalar dependências
    if not install_dependencies():
        return 1
    
    # Criar arquivo .env
    if not create_env_file():
        return 1
    
    # Criar diretórios
    if not create_directories():
        return 1
    
    # Exibir próximos passos
    show_next_steps()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
