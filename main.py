#!/usr/bin/env python3
"""
Script principal para executar o bot de marcação automática de LEDs no CVAT
"""
import argparse
import logging
import sys
import os
from pathlib import Path

from config import config, load_config_from_file
from cvat_bot import CVATLEDBot

def setup_logging(debug: bool = False):
    """Configura o sistema de logging"""
    level = logging.DEBUG if debug else logging.INFO
    
    # Configurar formato
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Handler para console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # Handler para arquivo
    log_file = Path("cvat_led_bot.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(formatter)
    
    # Configurar logger raiz
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Reduzir verbosidade de bibliotecas externas
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)

def validate_config():
    """Valida a configuração antes de executar"""
    errors = []
    
    if not config.cvat.host:
        errors.append("CVAT host não configurado")
    
    if not config.cvat.username:
        errors.append("CVAT username não configurado")
    
    if not config.cvat.password:
        errors.append("CVAT password não configurado")
    
    if errors:
        print("Erros de configuração encontrados:")
        for error in errors:
            print(f"  - {error}")
        print("\nConfigure as variáveis de ambiente ou use um arquivo de configuração.")
        return False
    
    return True

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Bot para marcação automática de LEDs no CVAT"
    )
    
    parser.add_argument(
        "task_id",
        type=int,
        help="ID da tarefa no CVAT para processar"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        help="Caminho para arquivo de configuração JSON"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Ativar modo debug"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Executar sem fazer alterações no CVAT"
    )
    
    parser.add_argument(
        "--save-debug-images",
        action="store_true",
        help="Salvar imagens de debug com detecções"
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    setup_logging(args.debug or config.debug_mode)
    logger = logging.getLogger(__name__)
    
    logger.info("Iniciando CVAT LED Bot")
    
    try:
        # Carregar configuração personalizada se fornecida
        if args.config:
            if not os.path.exists(args.config):
                logger.error(f"Arquivo de configuração não encontrado: {args.config}")
                return 1
            
            global config
            config = load_config_from_file(args.config)
            logger.info(f"Configuração carregada de {args.config}")
        
        # Aplicar argumentos da linha de comando
        if args.debug:
            config.debug_mode = True
        
        if args.save_debug_images:
            config.save_debug_images = True
        
        # Validar configuração
        if not validate_config():
            return 1
        
        # Criar diretório de debug se necessário
        if config.save_debug_images:
            debug_dir = Path(config.debug_output_dir)
            debug_dir.mkdir(exist_ok=True)
            logger.info(f"Imagens de debug serão salvas em {debug_dir}")
        
        # Executar bot
        if args.dry_run:
            logger.info("Modo dry-run ativado - nenhuma alteração será feita no CVAT")
            # TODO: Implementar modo dry-run
            return 0
        
        with CVATLEDBot(config) as bot:
            stats = bot.annotate_task(args.task_id)
            
            # Exibir estatísticas finais
            print("\n" + "="*50)
            print("ESTATÍSTICAS FINAIS")
            print("="*50)
            print(f"Tarefa ID: {stats['task_id']}")
            print(f"Jobs processados: {stats['processed_jobs']}/{stats['total_jobs']}")
            print(f"Total de anotações: {stats['total_annotations']}")
            print(f"LEDs funcionais: {stats['functional_leds']}")
            print(f"LEDs com defeito: {stats['defective_leds']}")
            print(f"LEDs desconhecidos: {stats['unknown_leds']}")
            print("="*50)
        
        logger.info("Bot executado com sucesso")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Execução interrompida pelo usuário")
        return 1
    except Exception as e:
        logger.error(f"Erro durante a execução: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
